# 🚀 <PERSON><PERSON><PERSON> <PERSON> Início <PERSON> - Takealot Scraper

## ⚡ Instalação Rápida (Windows)

1. **<PERSON><PERSON><PERSON> clique em `install.bat`** - Instala tudo automaticamente
2. **Execute**: `python run_scraper.py`
3. **Aguarde** - O scraper irá extrair os dados
4. **Abra** - `takealot_products.xlsx` com os resultados

## 🐍 Instalação Manual

```bash
# 1. Instalar dependências
pip install -r requirements.txt

# 2. Configurar
python setup.py

# 3. Executar
python run_scraper.py
```

## 🧪 Verificar Instalação

Verifique se todas as dependências foram instaladas corretamente executando o scraper com poucas categorias primeiro.

## 🎯 Uso Básico

### Scraper Completo (Recomendado)
```bash
python run_scraper.py
```
- Extrai 100 produtos de 10 categorias
- Ordena por número de avaliações
- Salva em Excel

### Scraper Simples
```bash
python takealot_scraper.py
```
- Versão básica sem configurações



## ⚙️ Configuração Rápida

Edite `config.py`:

```python
MAX_PRODUCTS = 50          # Menos produtos = mais rápido
HEADLESS_MODE = False      # Ver navegador funcionando
REQUEST_DELAY = 1          # Mais rápido (cuidado!)
```

## 📊 Resultados

O arquivo Excel contém:
- **Image URL** - Link da imagem
- **Product Description** - Nome do produto
- **Star Rating** - Classificação (0-5)
- **Number of Reviews** - Número de avaliações
- **Sale Price** - Preço em Rands

## 🔧 Solução de Problemas

### ❌ "Chrome driver not found"
```bash
python setup.py
```

### ❌ "No products found"
- Teste com `HEADLESS_MODE = False`
- Verifique conexão internet
- Aumente `REQUEST_DELAY`

### ❌ "Import error"
```bash
pip install -r requirements.txt
```

## 📁 Estrutura dos Arquivos

```
📦 takealot-scraper/
├── 🚀 install.bat              # Instalação automática (Windows)
├── 🐍 run_scraper.py           # ← EXECUTE ESTE
├── ⚙️ config.py                # Configurações
├── 📋 requirements.txt         # Dependências
├── 🔧 setup.py                 # Configuração inicial
├── 📚 README.md                # Documentação completa
├── 📖 QUICK_START.md           # Guia rápido
└── 📊 takealot_products.xlsx   # Resultado (gerado)
```

## 🎯 Comandos Essenciais

| Comando | Descrição |
|---------|-----------|
| `python run_scraper.py` | **Executar scraper completo** |
| `python setup.py` | Reconfigurar instalação |

## 💡 Dicas

- ✅ **Primeira vez**: Comece com poucas categorias
- ✅ **Problemas**: Veja os logs na pasta `logs/`
- ✅ **Personalizar**: Edite categorias em `config.py`
- ✅ **Mais rápido**: Reduza `MAX_PRODUCTS` e `REQUEST_DELAY`
- ✅ **Debug**: Use `HEADLESS_MODE = False`

## 🎉 Pronto!

Agora você tem uma automação completa para extrair dados do Takealot!

**Próximos passos:**
1. Execute `python run_scraper.py`
2. Aguarde a conclusão
3. Abra `takealot_products.xlsx`
4. Analise os dados ordenados por popularidade

---

💬 **Dúvidas?** Consulte o `README.md` para documentação completa.
