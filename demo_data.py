#!/usr/bin/env python3
"""
Demo data generator for Takealot Scraper
Creates sample data to demonstrate the Excel output format
"""

import pandas as pd
import random
from datetime import datetime

def generate_sample_data():
    """Generate sample product data similar to what would be scraped"""
    
    # Sample product data
    sample_products = [
        {
            'image_url': 'https://media.takealot.com/covers_images/laptop1.jpg',
            'description': 'HP Pavilion 15.6" Laptop - Intel Core i5, 8GB RAM, 256GB SSD',
            'star_rating': 4.5,
            'review_count': 1247,
            'price': 12999.00
        },
        {
            'image_url': 'https://media.takealot.com/covers_images/phone1.jpg',
            'description': 'Samsung Galaxy A54 5G 128GB - Awesome Violet',
            'star_rating': 4.3,
            'review_count': 892,
            'price': 8999.00
        },
        {
            'image_url': 'https://media.takealot.com/covers_images/tv1.jpg',
            'description': 'Samsung 55" 4K UHD Smart TV - Crystal UHD',
            'star_rating': 4.7,
            'review_count': 634,
            'price': 15999.00
        },
        {
            'image_url': 'https://media.takealot.com/covers_images/appliance1.jpg',
            'description': '<PERSON> Hobbs 1.7L Glass Kettle with Blue LED',
            'star_rating': 4.2,
            'review_count': 523,
            'price': 599.00
        },
        {
            'image_url': 'https://media.takealot.com/covers_images/skincare1.jpg',
            'description': 'CeraVe Hydrating Foaming Oil Cleanser 473ml',
            'star_rating': 4.6,
            'review_count': 456,
            'price': 289.00
        },
        {
            'image_url': 'https://media.takealot.com/covers_images/book1.jpg',
            'description': 'The Seven Husbands of Evelyn Hugo - Taylor Jenkins Reid',
            'star_rating': 4.8,
            'review_count': 389,
            'price': 199.00
        },
        {
            'image_url': 'https://media.takealot.com/covers_images/fitness1.jpg',
            'description': 'Adjustable Dumbbells Set 20kg - Home Gym Equipment',
            'star_rating': 4.1,
            'review_count': 267,
            'price': 1299.00
        },
        {
            'image_url': 'https://media.takealot.com/covers_images/baby1.jpg',
            'description': 'Philips Avent Natural Baby Bottle 260ml - 2 Pack',
            'star_rating': 4.4,
            'review_count': 234,
            'price': 179.00
        },
        {
            'image_url': 'https://media.takealot.com/covers_images/tshirt1.jpg',
            'description': 'Nike Dri-FIT Men\'s Training T-Shirt - Black',
            'star_rating': 4.3,
            'review_count': 198,
            'price': 449.00
        },
        {
            'image_url': 'https://media.takealot.com/covers_images/dress1.jpg',
            'description': 'Zara Floral Print Midi Dress - Summer Collection',
            'star_rating': 4.0,
            'review_count': 156,
            'price': 799.00
        },
        # Additional products with varying review counts
        {
            'image_url': 'https://media.takealot.com/covers_images/headphones1.jpg',
            'description': 'Sony WH-1000XM4 Wireless Noise Cancelling Headphones',
            'star_rating': 4.9,
            'review_count': 1456,
            'price': 6999.00
        },
        {
            'image_url': 'https://media.takealot.com/covers_images/watch1.jpg',
            'description': 'Apple Watch Series 9 GPS 45mm - Midnight Aluminum',
            'star_rating': 4.7,
            'review_count': 1123,
            'price': 8999.00
        },
        {
            'image_url': 'https://media.takealot.com/covers_images/camera1.jpg',
            'description': 'Canon EOS R50 Mirrorless Camera with 18-45mm Lens',
            'star_rating': 4.6,
            'review_count': 789,
            'price': 14999.00
        },
        {
            'image_url': 'https://media.takealot.com/covers_images/gaming1.jpg',
            'description': 'PlayStation 5 Console - Standard Edition',
            'star_rating': 4.8,
            'review_count': 2134,
            'price': 11999.00
        },
        {
            'image_url': 'https://media.takealot.com/covers_images/tablet1.jpg',
            'description': 'iPad Air 10.9" Wi-Fi 64GB - Space Grey',
            'star_rating': 4.5,
            'review_count': 967,
            'price': 9999.00
        }
    ]
    
    # Add some random variation to make it more realistic
    for product in sample_products:
        # Add small random variations to ratings
        product['star_rating'] += random.uniform(-0.2, 0.2)
        product['star_rating'] = max(1.0, min(5.0, round(product['star_rating'], 1)))
        
        # Add small random variations to review counts
        variation = random.randint(-50, 100)
        product['review_count'] = max(1, product['review_count'] + variation)
        
        # Add small random variations to prices
        price_variation = random.uniform(0.9, 1.1)
        product['price'] = round(product['price'] * price_variation, 2)
    
    return sample_products

def create_demo_excel():
    """Create demo Excel file with sample data"""
    print("Generating sample Takealot product data...")
    
    # Generate sample data
    products = generate_sample_data()
    
    # Sort by review count (descending) - this is the key requirement
    products_sorted = sorted(products, key=lambda x: x['review_count'], reverse=True)
    
    # Create DataFrame
    df = pd.DataFrame(products_sorted)
    
    # Rename columns to match the required format
    df.columns = ['Image URL', 'Product Description', 'Star Rating', 'Number of Reviews', 'Sale Price']
    
    # Save to Excel
    filename = 'takealot_products_demo.xlsx'
    df.to_excel(filename, index=False, engine='openpyxl')
    
    print(f"✅ Demo Excel file created: {filename}")
    print(f"📊 Total products: {len(products_sorted)}")
    
    # Show top 10 products by review count
    print("\n🏆 Top 10 Products by Review Count:")
    print("-" * 80)
    for i, product in enumerate(products_sorted[:10], 1):
        print(f"{i:2d}. {product['description'][:50]:<50} | {product['review_count']:4d} reviews | R{product['price']:8.2f}")
    
    # Show statistics
    review_counts = [p['review_count'] for p in products_sorted]
    prices = [p['price'] for p in products_sorted]
    ratings = [p['star_rating'] for p in products_sorted]
    
    print(f"\n📈 Statistics:")
    print(f"   Average reviews: {sum(review_counts) / len(review_counts):.1f}")
    print(f"   Average price: R{sum(prices) / len(prices):.2f}")
    print(f"   Average rating: {sum(ratings) / len(ratings):.2f}")
    print(f"   Most reviewed: {max(review_counts)} reviews")
    print(f"   Highest price: R{max(prices):.2f}")
    print(f"   Best rating: {max(ratings):.1f} stars")
    
    return filename

def show_excel_structure():
    """Show the structure of the Excel file"""
    print("\n📋 Excel File Structure:")
    print("=" * 60)
    print("Column A: Image URL          - URL of product image")
    print("Column B: Product Description - Full product name/title")
    print("Column C: Star Rating        - Rating from 1-5 stars")
    print("Column D: Number of Reviews  - Count of customer reviews")
    print("Column E: Sale Price         - Current price in Rands (R)")
    print("=" * 60)
    print("✅ Products are sorted by 'Number of Reviews' (descending)")
    print("✅ This matches the Takealot requirement exactly")

if __name__ == "__main__":
    print("🛒 Takealot Scraper - Demo Data Generator")
    print("=" * 50)
    
    # Create demo Excel file
    filename = create_demo_excel()
    
    # Show structure
    show_excel_structure()
    
    print(f"\n🎉 Demo completed!")
    print(f"📁 Open '{filename}' to see the results")
    print(f"🔧 This demonstrates exactly what the real scraper will produce")
