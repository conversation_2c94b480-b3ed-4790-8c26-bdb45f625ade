@echo off
echo ========================================
echo    Takealot Scraper - Instalacao
echo ========================================
echo.

echo Verificando Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERRO: Python nao encontrado!
    echo Por favor, instale Python 3.7+ de https://python.org
    pause
    exit /b 1
)

echo Python encontrado!
echo.

echo Instalando dependencias...
python -m pip install --upgrade pip
python -m pip install -r requirements.txt

if errorlevel 1 (
    echo ERRO: Falha na instalacao das dependencias
    pause
    exit /b 1
)

echo.
echo Configurando Chrome driver...
python setup.py

if errorlevel 1 (
    echo ERRO: Falha na configuracao
    pause
    exit /b 1
)

echo.
echo ========================================
echo    Instalacao Concluida!
echo ========================================
echo.
echo Para usar o scraper:
echo   python run_scraper.py
echo.
echo Para testar:
echo   python test_scraper.py
echo.
echo Para demo:
echo   python demo.py
echo.
pause
