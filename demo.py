#!/usr/bin/env python3
"""
Demo script for Takealot Scraper
Shows how to use the scraper with custom settings
"""

import sys
import os
from pathlib import Path

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from takealot_scraper import TakealotScraper
except ImportError as e:
    print(f"Import error: {e}")
    print("Please run setup.py first: python setup.py")
    sys.exit(1)

def demo_quick_scrape():
    """Demo: Quick scrape of a few products"""
    print("=== Demo: Quick Scrape ===")
    print("Scraping 10 products from smartphones category...\n")
    
    scraper = TakealotScraper(headless=True)
    
    try:
        # Single category URL
        url = "https://www.takealot.com/all-categories/mobile-tablets-wearables/mobile-phones/smartphones"
        
        # Scrape 10 products
        products = scraper.scrape_product_page(url, max_products=10)
        
        if products:
            # Sort by reviews
            sorted_products = scraper.sort_products_by_reviews(products)
            
            # Show top 5
            print("Top 5 products by review count:")
            for i, product in enumerate(sorted_products[:5], 1):
                print(f"{i}. {product['description'][:50]}...")
                print(f"   Reviews: {product['review_count']}, Rating: {product['star_rating']}, Price: R{product['price']}")
            
            # Save to Excel
            scraper.save_to_excel(sorted_products, "demo_smartphones.xlsx")
            print(f"\n✅ Results saved to: demo_smartphones.xlsx")
        else:
            print("❌ No products found")
    
    finally:
        scraper.driver.quit()

def demo_custom_categories():
    """Demo: Custom category selection"""
    print("\n=== Demo: Custom Categories ===")
    print("Scraping from custom category list...\n")
    
    # Custom categories
    custom_categories = [
        "https://www.takealot.com/all-categories/computers-gaming/laptops-computers/laptops",
        "https://www.takealot.com/all-categories/tv-audio-gaming/televisions/smart-tvs"
    ]
    
    scraper = TakealotScraper(headless=True)
    
    try:
        all_products = []
        
        for i, url in enumerate(custom_categories, 1):
            print(f"Scraping category {i}/{len(custom_categories)}...")
            products = scraper.scrape_product_page(url, max_products=15)
            all_products.extend(products)
        
        if all_products:
            # Sort by reviews
            sorted_products = scraper.sort_products_by_reviews(all_products)
            
            print(f"\nTotal products found: {len(sorted_products)}")
            
            # Show statistics
            review_counts = [p['review_count'] for p in sorted_products if p['review_count'] > 0]
            if review_counts:
                avg_reviews = sum(review_counts) / len(review_counts)
                print(f"Average reviews: {avg_reviews:.1f}")
                print(f"Most reviewed product: {max(review_counts)} reviews")
            
            # Save results
            scraper.save_to_excel(sorted_products, "demo_custom_categories.xlsx")
            print(f"✅ Results saved to: demo_custom_categories.xlsx")
        else:
            print("❌ No products found")
    
    finally:
        scraper.driver.quit()

def demo_data_analysis():
    """Demo: Basic data analysis of scraped products"""
    print("\n=== Demo: Data Analysis ===")
    
    try:
        import pandas as pd
        
        # Check if we have demo data
        demo_file = "demo_smartphones.xlsx"
        if not Path(demo_file).exists():
            print(f"Demo file {demo_file} not found. Running quick scrape first...")
            demo_quick_scrape()
        
        if Path(demo_file).exists():
            # Load data
            df = pd.read_excel(demo_file)
            print(f"Loaded {len(df)} products for analysis\n")
            
            # Basic statistics
            print("=== Data Analysis Results ===")
            print(f"Total products: {len(df)}")
            
            # Review statistics
            reviews = df['Number of Reviews']
            print(f"\nReview Statistics:")
            print(f"  Average reviews: {reviews.mean():.1f}")
            print(f"  Median reviews: {reviews.median():.1f}")
            print(f"  Max reviews: {reviews.max()}")
            print(f"  Min reviews: {reviews.min()}")
            
            # Price statistics
            prices = df['Sale Price']
            valid_prices = prices[prices > 0]
            if len(valid_prices) > 0:
                print(f"\nPrice Statistics:")
                print(f"  Average price: R{valid_prices.mean():.2f}")
                print(f"  Median price: R{valid_prices.median():.2f}")
                print(f"  Max price: R{valid_prices.max():.2f}")
                print(f"  Min price: R{valid_prices.min():.2f}")
            
            # Rating statistics
            ratings = df['Star Rating']
            valid_ratings = ratings[ratings > 0]
            if len(valid_ratings) > 0:
                print(f"\nRating Statistics:")
                print(f"  Average rating: {valid_ratings.mean():.2f}")
                print(f"  Median rating: {valid_ratings.median():.2f}")
                print(f"  Max rating: {valid_ratings.max():.2f}")
                print(f"  Min rating: {valid_ratings.min():.2f}")
            
            # Top products
            print(f"\nTop 3 Most Reviewed Products:")
            top_products = df.nlargest(3, 'Number of Reviews')
            for i, (_, product) in enumerate(top_products.iterrows(), 1):
                print(f"{i}. {product['Product Description'][:60]}...")
                print(f"   {product['Number of Reviews']} reviews, {product['Star Rating']} stars, R{product['Sale Price']}")
        
    except ImportError:
        print("pandas not available for data analysis")
    except Exception as e:
        print(f"Error in data analysis: {e}")

def main():
    """Main demo function"""
    print("Takealot Scraper Demo")
    print("=" * 50)
    
    try:
        # Demo 1: Quick scrape
        demo_quick_scrape()
        
        # Demo 2: Custom categories
        demo_custom_categories()
        
        # Demo 3: Data analysis
        demo_data_analysis()
        
        print("\n" + "=" * 50)
        print("Demo completed! Check the generated Excel files:")
        print("- demo_smartphones.xlsx")
        print("- demo_custom_categories.xlsx")
        print("\nTo run the full scraper: python run_scraper.py")
        
    except KeyboardInterrupt:
        print("\nDemo interrupted by user")
    except Exception as e:
        print(f"Demo failed: {e}")

if __name__ == "__main__":
    main()
