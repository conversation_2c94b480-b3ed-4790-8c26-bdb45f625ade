#!/usr/bin/env python3
"""
Test script for Takealot Scraper
Tests basic functionality with a small sample
"""

import sys
import os
import logging
from pathlib import Path

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from takealot_scraper import TakealotScraper
except ImportError as e:
    print(f"Import error: {e}")
    print("Please run setup.py first: python setup.py")
    sys.exit(1)

def test_basic_functionality():
    """Test basic scraper functionality"""
    print("=== Testing Takealot Scraper ===\n")
    
    # Create scraper instance
    scraper = TakealotScraper(headless=False)  # Show browser for testing
    
    try:
        # Test with one category and few products
        test_url = "https://www.takealot.com/all-categories/computers-gaming/laptops-computers/laptops"
        
        print(f"Testing with URL: {test_url}")
        print("This will open a browser window - this is normal for testing\n")
        
        # Scrape a few products
        products = scraper.scrape_product_page(test_url, max_products=5)
        
        if not products:
            print("❌ No products found - this might indicate an issue")
            return False
        
        print(f"✅ Successfully scraped {len(products)} products")
        
        # Display sample data
        print("\n--- Sample Product Data ---")
        for i, product in enumerate(products[:3], 1):
            print(f"\nProduct {i}:")
            print(f"  Description: {product.get('description', 'N/A')[:60]}...")
            print(f"  Star Rating: {product.get('star_rating', 'N/A')}")
            print(f"  Reviews: {product.get('review_count', 'N/A')}")
            print(f"  Price: R{product.get('price', 'N/A')}")
            print(f"  Image URL: {product.get('image_url', 'N/A')[:50]}...")
        
        # Test sorting
        sorted_products = scraper.sort_products_by_reviews(products)
        print(f"\n✅ Sorting test passed")
        
        # Test Excel export
        test_filename = "test_output.xlsx"
        scraper.save_to_excel(sorted_products, test_filename)
        
        if Path(test_filename).exists():
            print(f"✅ Excel export test passed - file created: {test_filename}")
            # Clean up test file
            os.remove(test_filename)
            print("✅ Test file cleaned up")
        else:
            print("❌ Excel export test failed")
            return False
        
        print("\n=== All Tests Passed! ===")
        print("The scraper is working correctly.")
        print("You can now run the full scraper with: python run_scraper.py")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False
        
    finally:
        if scraper.driver:
            scraper.driver.quit()
            print("Browser closed")

def test_configuration():
    """Test configuration file"""
    try:
        import config
        print("✅ Configuration file loaded successfully")
        
        required_attrs = ['MAX_PRODUCTS', 'CATEGORIES', 'OUTPUT_FILE']
        for attr in required_attrs:
            if hasattr(config, attr):
                print(f"✅ {attr}: {getattr(config, attr)}")
            else:
                print(f"❌ Missing configuration: {attr}")
                return False
        
        return True
        
    except ImportError:
        print("❌ Configuration file not found")
        print("Please run setup.py first: python setup.py")
        return False

def main():
    """Main test function"""
    print("Starting Takealot Scraper Tests...\n")
    
    # Test configuration
    if not test_configuration():
        print("\nConfiguration test failed. Please run setup.py first.")
        return 1
    
    print("\n" + "-"*50)
    
    # Test basic functionality
    if not test_basic_functionality():
        print("\nFunctionality test failed. Please check the error messages above.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
