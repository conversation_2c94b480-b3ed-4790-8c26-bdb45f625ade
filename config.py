# Takealot Scraper Configuration

# Maximum number of products to scrape
MAX_PRODUCTS = 100

# Maximum products per category
MAX_PRODUCTS_PER_CATEGORY = 20

# Delay between requests (seconds)
REQUEST_DELAY = 2

# Chrome driver options
HEADLESS_MODE = True
WINDOW_SIZE = "1920,1080"

# Output file name
OUTPUT_FILE = "takealot_products.xlsx"

# Categories to scrape (you can modify this list)
CATEGORIES = [
    "https://www.takealot.com/all-categories/computers-gaming/laptops-computers/laptops",
    "https://www.takealot.com/all-categories/mobile-tablets-wearables/mobile-phones/smartphones",
    "https://www.takealot.com/all-categories/tv-audio-gaming/televisions/smart-tvs",
    "https://www.takealot.com/all-categories/home-garden/kitchen-dining/small-appliances",
    "https://www.takealot.com/all-categories/beauty-health/skincare/face-care",
    "https://www.takealot.com/all-categories/books-media/books/fiction",
    "https://www.takealot.com/all-categories/sports-outdoors/fitness/gym-equipment",
    "https://www.takealot.com/all-categories/baby-toddler/feeding/bottles-accessories",
    "https://www.takealot.com/all-categories/fashion/mens-clothing/t-shirts-vests",
    "https://www.takealot.com/all-categories/fashion/womens-clothing/dresses"
]
