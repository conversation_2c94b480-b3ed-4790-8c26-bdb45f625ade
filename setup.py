#!/usr/bin/env python3
"""
Setup script for Takealot Scraper
Installs dependencies and sets up Chrome driver
"""

import subprocess
import sys
import os
from pathlib import Path

def install_requirements():
    """Install Python requirements"""
    print("Installing Python requirements...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Requirements installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"✗ Error installing requirements: {e}")
        return False
    return True

def setup_chrome_driver():
    """Setup Chrome driver using webdriver-manager"""
    print("Setting up Chrome driver...")
    try:
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        
        # Download and setup ChromeDriver
        driver_path = ChromeDriverManager().install()
        print(f"✓ Chrome driver installed at: {driver_path}")
        
        # Test driver
        service = Service(driver_path)
        options = webdriver.ChromeOptions()
        options.add_argument("--headless")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        
        driver = webdriver.Chrome(service=service, options=options)
        driver.get("https://www.google.com")
        driver.quit()
        print("✓ Chrome driver test successful")
        
    except Exception as e:
        print(f"✗ Error setting up Chrome driver: {e}")
        print("Please make sure Chrome browser is installed on your system")
        return False
    return True

def create_config_file():
    """Create configuration file"""
    config_content = """# Takealot Scraper Configuration

# Maximum number of products to scrape
MAX_PRODUCTS = 100

# Maximum products per category
MAX_PRODUCTS_PER_CATEGORY = 20

# Delay between requests (seconds)
REQUEST_DELAY = 2

# Chrome driver options
HEADLESS_MODE = True
WINDOW_SIZE = "1920,1080"

# Output file name
OUTPUT_FILE = "takealot_products.xlsx"

# Categories to scrape (you can modify this list)
CATEGORIES = [
    "https://www.takealot.com/all-categories/computers-gaming/laptops-computers/laptops",
    "https://www.takealot.com/all-categories/mobile-tablets-wearables/mobile-phones/smartphones",
    "https://www.takealot.com/all-categories/tv-audio-gaming/televisions/smart-tvs",
    "https://www.takealot.com/all-categories/home-garden/kitchen-dining/small-appliances",
    "https://www.takealot.com/all-categories/beauty-health/skincare/face-care"
]
"""
    
    with open("config.py", "w") as f:
        f.write(config_content)
    print("✓ Configuration file created: config.py")

def main():
    """Main setup function"""
    print("=== Takealot Scraper Setup ===\n")
    
    # Install requirements
    if not install_requirements():
        print("Setup failed. Please check the error messages above.")
        return
    
    # Setup Chrome driver
    if not setup_chrome_driver():
        print("Setup failed. Please check the error messages above.")
        return
    
    # Create config file
    create_config_file()
    
    print("\n=== Setup Complete ===")
    print("You can now run the scraper with:")
    print("python takealot_scraper.py")
    print("\nOr use the enhanced version:")
    print("python run_scraper.py")

if __name__ == "__main__":
    main()
