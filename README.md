# Takealot Product Scraper

Uma automação de raspagem de dados para extrair informações de produtos do site Takealot.com, organizando os dados por número de avaliações e exportando para Excel.

## Funcionalidades

- ✅ Extrai dados de produtos de múltiplas categorias do Takealot
- ✅ Ordena produtos por número de avaliações (ordem decrescente)
- ✅ Captura informações completas: imagem, descrição, classificação, avaliações, preço
- ✅ Exporta dados para planilha Excel (.xlsx)
- ✅ Configuração flexível de categorias e limites
- ✅ Logging detalhado e tratamento de erros
- ✅ Interface de linha de comando amigável

## Dados Extraídos

Para cada produto, o scraper captura:

1. **Link do produto** - URL direta para a página do produto
2. **Imagem do produto** - URL da imagem principal
3. **Descrição do produto** - Nome/título completo
4. **Classificação por estrelas** - Rating de 1-5 estrelas
5. **Número de avaliações** - Quantidade de reviews (usado para ordenação)
6. **Preço de venda** - Preço atual em Rands (R)

## Pré-requisitos

- Python 3.7 ou superior
- Google Chrome instalado
- Conexão com internet

## Instalação

### 1. Clone ou baixe os arquivos

Certifique-se de ter todos os arquivos do projeto:
- `takealot_scraper.py` - Scraper principal
- `run_scraper.py` - Script executável melhorado
- `setup.py` - Script de configuração
- `requirements.txt` - Dependências Python
- `README.md` - Este arquivo

### 2. Execute o setup

```bash
python setup.py
```

Este comando irá:
- Instalar todas as dependências Python necessárias
- Configurar o ChromeDriver automaticamente
- Criar arquivo de configuração (`config.py`)
- Testar a instalação

### 3. (Opcional) Configurar categorias

Edite o arquivo `config.py` para personalizar:
- Número máximo de produtos
- Categorias a serem raspadas
- Nome do arquivo de saída
- Outras configurações

## Uso

### Método Simples
```bash
python takealot_scraper.py
```

### Método Recomendado (com configurações)
```bash
python run_scraper.py
```

## Configuração

O arquivo `config.py` permite personalizar:

```python
# Número máximo de produtos a extrair
MAX_PRODUCTS = 100

# Máximo de produtos por categoria
MAX_PRODUCTS_PER_CATEGORY = 20

# Delay entre requisições (segundos)
REQUEST_DELAY = 2

# Modo headless (True = sem interface gráfica)
HEADLESS_MODE = True

# Nome do arquivo de saída
OUTPUT_FILE = "takealot_products.xlsx"

# Categorias para raspar
CATEGORIES = [
    "https://www.takealot.com/all-categories/computers-gaming/laptops-computers/laptops",
    "https://www.takealot.com/all-categories/mobile-tablets-wearables/mobile-phones/smartphones",
    # ... adicione mais categorias conforme necessário
]
```

## Estrutura do Arquivo Excel

O arquivo Excel gerado contém as seguintes colunas:

| Coluna | Descrição |
|--------|-----------|
| Product URL | URL direta para a página do produto |
| Image URL | URL da imagem do produto |
| Product Description | Nome/descrição completa |
| Star Rating | Classificação em estrelas (0-5) |
| Number of Reviews | Número de avaliações (usado para ordenação) |
| Sale Price | Preço de venda em Rands |

## Categorias Pré-configuradas

O scraper vem com categorias populares pré-configuradas:

- 💻 Laptops e Computadores
- 📱 Smartphones
- 📺 Smart TVs
- 🏠 Eletrodomésticos de Cozinha
- 💄 Cuidados com a Pele
- 📚 Livros de Ficção
- 🏋️ Equipamentos de Ginástica
- 👶 Acessórios para Bebês
- 👕 Roupas Masculinas
- 👗 Roupas Femininas

## Logs e Monitoramento

- Logs são salvos na pasta `logs/` com timestamp
- Progresso é exibido no console em tempo real
- Estatísticas detalhadas são mostradas ao final

## Tratamento de Erros

O scraper inclui tratamento robusto de erros:
- Retry automático para elementos não encontrados
- Múltiplos seletores CSS para maior compatibilidade
- Continuação mesmo se algumas categorias falharem
- Logs detalhados para debugging

## Limitações e Considerações

- ⚠️ Respeite os termos de uso do Takealot
- ⚠️ Use delays apropriados entre requisições
- ⚠️ O site pode mudar sua estrutura, requerendo atualizações
- ⚠️ Alguns produtos podem não ter todos os campos disponíveis

## Solução de Problemas

### Chrome Driver não encontrado
```bash
# Execute novamente o setup
python setup.py
```

### Erro de importação
```bash
# Instale dependências manualmente
pip install -r requirements.txt
```

### Produtos não encontrados
- Verifique se as URLs das categorias estão corretas
- Teste com `HEADLESS_MODE = False` para ver o navegador
- Verifique os logs para erros específicos

### Timeout errors
- Aumente o `REQUEST_DELAY` no config.py
- Verifique sua conexão com internet
- Teste com menos categorias primeiro

## Estrutura do Projeto

```
takealot-scraper/
├── takealot_scraper.py     # Classe principal do scraper
├── run_scraper.py          # Script executável melhorado
├── setup.py                # Script de instalação
├── config.py               # Configurações
├── requirements.txt        # Dependências Python
├── install.bat             # Instalação automática (Windows)
├── README.md              # Documentação principal
├── QUICK_START.md         # Guia de início rápido
├── .gitignore             # Arquivos a ignorar
├── logs/                  # Pasta de logs (criada automaticamente)
└── takealot_products.xlsx # Arquivo de saída (gerado)
```

## Metodologia Técnica

1. **Mapeamento de Categorias**: URLs pré-definidas para categorias populares
2. **Coleta de Dados**: Selenium WebDriver para JavaScript rendering
3. **Parsing**: Múltiplos seletores CSS para robustez
4. **Ordenação**: Por número de avaliações (decrescente)
5. **Exportação**: Pandas DataFrame para Excel via openpyxl
6. **Validação**: Verificação de consistência e sanity checks

## Contribuição

Para melhorar o scraper:
1. Adicione novas categorias no `config.py`
2. Melhore os seletores CSS se o site mudar
3. Adicione novos campos de dados conforme necessário
4. Otimize performance e tratamento de erros

## Licença

Este projeto é para fins educacionais e de pesquisa. Use responsavelmente e respeite os termos de uso do Takealot.
