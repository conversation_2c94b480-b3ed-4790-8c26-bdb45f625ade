#!/usr/bin/env python3
"""
Enhanced Takealot Scraper Runner
Uses configuration file and provides better error handling
"""

import sys
import os
import logging
from datetime import datetime
from pathlib import Path

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from takealot_scraper import TakealotScraper
    import config
except ImportError as e:
    print(f"Import error: {e}")
    print("Please run setup.py first: python setup.py")
    sys.exit(1)

def setup_logging():
    """Setup logging with file and console output"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"scraper_{timestamp}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return logging.getLogger(__name__)

def validate_config():
    """Validate configuration settings"""
    required_attrs = ['MAX_PRODUCTS', 'CATEGORIES', 'OUTPUT_FILE']
    
    for attr in required_attrs:
        if not hasattr(config, attr):
            raise ValueError(f"Missing required configuration: {attr}")
    
    if not config.CATEGORIES:
        raise ValueError("No categories specified in configuration")
    
    if config.MAX_PRODUCTS <= 0:
        raise ValueError("MAX_PRODUCTS must be greater than 0")

class EnhancedTakealotScraper(TakealotScraper):
    """Enhanced scraper with configuration support"""
    
    def __init__(self):
        super().__init__(headless=getattr(config, 'HEADLESS_MODE', True))
        self.max_products = config.MAX_PRODUCTS
        self.max_products_per_category = getattr(config, 'MAX_PRODUCTS_PER_CATEGORY', 20)
        self.request_delay = getattr(config, 'REQUEST_DELAY', 2)
        self.output_file = config.OUTPUT_FILE
        self.categories = config.CATEGORIES
    
    def get_category_urls(self):
        """Use configured category URLs"""
        logger.info(f"Using {len(self.categories)} configured categories")
        return self.categories
    
    def scrape_categories(self, category_urls, max_products_per_category=None):
        """Enhanced category scraping with progress tracking"""
        if max_products_per_category is None:
            max_products_per_category = self.max_products_per_category
        
        all_products = []
        successful_categories = 0
        
        for i, url in enumerate(category_urls):
            logger.info(f"Scraping category {i+1}/{len(category_urls)}: {url}")
            
            try:
                products = self.scrape_product_page(url, max_products_per_category)
                if products:
                    all_products.extend(products)
                    successful_categories += 1
                    logger.info(f"Successfully scraped {len(products)} products from category {i+1}")
                else:
                    logger.warning(f"No products found in category {i+1}")
                
            except Exception as e:
                logger.error(f"Failed to scrape category {i+1}: {e}")
                continue
            
            # Add delay between categories
            if i < len(category_urls) - 1:  # Don't delay after last category
                import time
                time.sleep(self.request_delay)
        
        logger.info(f"Scraping completed. {successful_categories}/{len(category_urls)} categories successful")
        return all_products
    
    def run_scraper(self):
        """Enhanced main scraper method"""
        try:
            logger.info("Starting enhanced Takealot scraper...")
            logger.info(f"Configuration: {self.max_products} max products, {len(self.categories)} categories")
            
            # Get category URLs
            category_urls = self.get_category_urls()
            
            # Scrape products
            all_products = self.scrape_categories(category_urls)
            
            if not all_products:
                logger.error("No products scraped from any category")
                return False
            
            logger.info(f"Total products scraped: {len(all_products)}")
            
            # Sort by review count
            sorted_products = self.sort_products_by_reviews(all_products)
            logger.info("Products sorted by review count")
            
            # Take top products
            top_products = sorted_products[:self.max_products]
            logger.info(f"Selected top {len(top_products)} products")
            
            # Save to Excel
            self.save_to_excel(top_products, self.output_file)
            
            # Print summary
            self.print_summary(top_products)
            
            return True
            
        except Exception as e:
            logger.error(f"Error in enhanced scraper: {e}")
            return False
        finally:
            if self.driver:
                self.driver.quit()
                logger.info("Browser closed")
    
    def print_summary(self, products):
        """Print scraping summary"""
        if not products:
            return
        
        print("\n" + "="*60)
        print("SCRAPING SUMMARY")
        print("="*60)
        print(f"Total products scraped: {len(products)}")
        print(f"Output file: {self.output_file}")
        
        # Top 5 products by reviews
        print("\nTop 5 products by review count:")
        for i, product in enumerate(products[:5], 1):
            print(f"{i}. {product['description'][:50]}... ({product['review_count']} reviews)")
        
        # Statistics
        review_counts = [p['review_count'] for p in products if p['review_count'] > 0]
        if review_counts:
            print(f"\nReview statistics:")
            print(f"  Average reviews: {sum(review_counts) / len(review_counts):.1f}")
            print(f"  Max reviews: {max(review_counts)}")
            print(f"  Min reviews: {min(review_counts)}")
        
        prices = [p['price'] for p in products if p['price'] > 0]
        if prices:
            print(f"\nPrice statistics:")
            print(f"  Average price: R{sum(prices) / len(prices):.2f}")
            print(f"  Max price: R{max(prices):.2f}")
            print(f"  Min price: R{min(prices):.2f}")
        
        print("="*60)

def main():
    """Main function"""
    global logger
    logger = setup_logging()
    
    try:
        # Validate configuration
        validate_config()
        logger.info("Configuration validated successfully")
        
        # Create and run scraper
        scraper = EnhancedTakealotScraper()
        success = scraper.run_scraper()
        
        if success:
            print(f"\n✓ Scraping completed successfully!")
            print(f"Results saved to: {config.OUTPUT_FILE}")
        else:
            print(f"\n✗ Scraping failed. Check logs for details.")
            return 1
            
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        print(f"\n✗ Fatal error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
