#!/usr/bin/env python3
"""
Takealot Product Scraper
Scrapes product data from Takealot.com and exports to Excel
"""

import time
import re
import pandas as pd
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import requests
from urllib.parse import urljoin, urlparse
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TakealotScraper:
    def __init__(self, headless=True):
        """Initialize the scraper with Chrome driver"""
        self.driver = None
        self.base_url = "https://www.takealot.com"
        self.products = []
        self.setup_driver(headless)
    
    def setup_driver(self, headless=True):
        """Setup Chrome driver with appropriate options"""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(10)
            logger.info("Chrome driver initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Chrome driver: {e}")
            raise
    
    def get_category_urls(self):
        """Get predefined category URLs for popular categories"""
        # Popular categories on Takealot
        category_urls = [
            "https://www.takealot.com/all-categories/computers-gaming/laptops-computers/laptops",
            "https://www.takealot.com/all-categories/mobile-tablets-wearables/mobile-phones/smartphones",
            "https://www.takealot.com/all-categories/tv-audio-gaming/televisions/smart-tvs",
            "https://www.takealot.com/all-categories/home-garden/kitchen-dining/small-appliances",
            "https://www.takealot.com/all-categories/beauty-health/skincare/face-care",
            "https://www.takealot.com/all-categories/books-media/books/fiction",
            "https://www.takealot.com/all-categories/sports-outdoors/fitness/gym-equipment",
            "https://www.takealot.com/all-categories/baby-toddler/feeding/bottles-accessories",
            "https://www.takealot.com/all-categories/fashion/mens-clothing/t-shirts-vests",
            "https://www.takealot.com/all-categories/fashion/womens-clothing/dresses"
        ]

        logger.info(f"Using {len(category_urls)} predefined category URLs")
        return category_urls
    
    def scrape_product_page(self, url, max_products=100):
        """Scrape products from a category page"""
        try:
            self.driver.get(url)
            time.sleep(5)

            # Try multiple selectors for product cards
            product_selectors = [
                ".product-card",
                "[data-ref='product-card']",
                ".product-item",
                ".grid-item",
                "article[data-module='product-card']"
            ]

            product_cards = []
            for selector in product_selectors:
                try:
                    WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    product_cards = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if product_cards:
                        logger.info(f"Found {len(product_cards)} products using selector: {selector}")
                        break
                except TimeoutException:
                    continue

            if not product_cards:
                logger.warning(f"No products found on page: {url}")
                return []

            products_data = []

            for i, card in enumerate(product_cards[:max_products]):
                if i >= max_products:
                    break

                try:
                    product_data = self.extract_product_data(card)
                    if product_data:
                        products_data.append(product_data)
                        logger.info(f"Extracted product {i+1}: {product_data['description'][:50]}...")
                except Exception as e:
                    logger.warning(f"Error extracting product {i+1}: {e}")
                    continue

            return products_data

        except Exception as e:
            logger.error(f"Error scraping page {url}: {e}")
            return []
    
    def extract_product_data(self, card):
        """Extract data from a single product card"""
        try:
            product_data = {}

            # Product image - try multiple selectors
            img_selectors = ["img", ".product-image img", ".image img", "[data-ref='product-image'] img"]
            for selector in img_selectors:
                try:
                    img_element = card.find_element(By.CSS_SELECTOR, selector)
                    product_data['image_url'] = (img_element.get_attribute('src') or
                                               img_element.get_attribute('data-src') or
                                               img_element.get_attribute('data-lazy-src'))
                    if product_data['image_url']:
                        break
                except NoSuchElementException:
                    continue
            if 'image_url' not in product_data:
                product_data['image_url'] = ""

            # Product description/title - try multiple selectors
            title_selectors = [
                "[data-ref='product-title']",
                ".product-title",
                "h3",
                "h4",
                ".title",
                "a[title]"
            ]
            for selector in title_selectors:
                try:
                    title_element = card.find_element(By.CSS_SELECTOR, selector)
                    title_text = title_element.text.strip() or title_element.get_attribute('title')
                    if title_text:
                        product_data['description'] = title_text
                        break
                except NoSuchElementException:
                    continue
            if 'description' not in product_data:
                product_data['description'] = ""

            # Star rating - try multiple approaches
            rating_selectors = [
                "[data-ref='star-rating']",
                ".star-rating",
                ".rating",
                ".stars"
            ]
            product_data['star_rating'] = 0
            for selector in rating_selectors:
                try:
                    rating_element = card.find_element(By.CSS_SELECTOR, selector)
                    rating_text = rating_element.get_attribute('aria-label') or rating_element.text
                    rating_match = re.search(r'(\d+(?:\.\d+)?)', rating_text)
                    if rating_match:
                        product_data['star_rating'] = float(rating_match.group(1))
                        break
                except NoSuchElementException:
                    continue

            # Number of reviews - look for text with parentheses
            review_selectors = [
                "[data-ref='review-count']",
                ".review-count",
                ".reviews",
                ".rating-count"
            ]
            product_data['review_count'] = 0

            # Search entire card text for review count in parentheses
            card_text = card.text
            review_match = re.search(r'\((\d+)\)', card_text)
            if review_match:
                product_data['review_count'] = int(review_match.group(1))
            else:
                # Try specific selectors
                for selector in review_selectors:
                    try:
                        review_element = card.find_element(By.CSS_SELECTOR, selector)
                        review_text = review_element.text
                        review_match = re.search(r'(\d+)', review_text)
                        if review_match:
                            product_data['review_count'] = int(review_match.group(1))
                            break
                    except NoSuchElementException:
                        continue

            # Price - try multiple selectors
            price_selectors = [
                "[data-ref='product-price']",
                ".price",
                ".product-price",
                ".current-price",
                ".selling-price"
            ]
            product_data['price'] = 0
            for selector in price_selectors:
                try:
                    price_element = card.find_element(By.CSS_SELECTOR, selector)
                    price_text = price_element.text
                    price_match = re.search(r'R\s*(\d+(?:,\d+)*(?:\.\d+)?)', price_text)
                    if price_match:
                        price_str = price_match.group(1).replace(',', '')
                        product_data['price'] = float(price_str)
                        break
                except NoSuchElementException:
                    continue

            return product_data if product_data.get('description') else None

        except Exception as e:
            logger.error(f"Error extracting product data: {e}")
            return None
    
    def scrape_categories(self, category_urls, max_products_per_category=100):
        """Scrape multiple categories"""
        all_products = []
        
        for i, url in enumerate(category_urls):
            logger.info(f"Scraping category {i+1}/{len(category_urls)}: {url}")
            products = self.scrape_product_page(url, max_products_per_category)
            all_products.extend(products)
            
            # Add delay between categories
            time.sleep(2)
        
        return all_products
    
    def sort_products_by_reviews(self, products):
        """Sort products by review count in descending order"""
        return sorted(products, key=lambda x: x.get('review_count', 0), reverse=True)
    
    def save_to_excel(self, products, filename='takealot_products.xlsx'):
        """Save products to Excel file"""
        if not products:
            logger.warning("No products to save")
            return
        
        # Create DataFrame
        df = pd.DataFrame(products)
        
        # Reorder columns
        column_order = ['image_url', 'description', 'star_rating', 'review_count', 'price']
        df = df.reindex(columns=column_order)
        
        # Rename columns for better readability
        df.columns = ['Image URL', 'Product Description', 'Star Rating', 'Number of Reviews', 'Sale Price']
        
        # Save to Excel
        df.to_excel(filename, index=False, engine='openpyxl')
        logger.info(f"Saved {len(products)} products to {filename}")
    
    def run_scraper(self, max_products=100):
        """Main method to run the scraper"""
        try:
            logger.info("Starting Takealot scraper...")
            
            # Get category URLs
            category_urls = self.get_category_urls()
            if not category_urls:
                logger.error("No category URLs found")
                return
            
            # Scrape products
            all_products = self.scrape_categories(category_urls, max_products // len(category_urls))
            
            if not all_products:
                logger.error("No products scraped")
                return
            
            # Sort by review count
            sorted_products = self.sort_products_by_reviews(all_products)
            
            # Take top products
            top_products = sorted_products[:max_products]
            
            # Save to Excel
            self.save_to_excel(top_products)
            
            logger.info(f"Scraping completed. Found {len(top_products)} products.")
            
        except Exception as e:
            logger.error(f"Error in main scraper: {e}")
        finally:
            if self.driver:
                self.driver.quit()

if __name__ == "__main__":
    scraper = TakealotScraper(headless=False)  # Set to True for headless mode
    scraper.run_scraper(max_products=100)
