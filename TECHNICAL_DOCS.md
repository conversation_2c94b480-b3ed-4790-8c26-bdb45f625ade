# 📋 Documentação Técnica - Takealot Scraper

## 🏗️ Arquitetura do Sistema

### Componentes Principais

1. **`takealot_scraper.py`** - Classe principal do scraper
2. **`run_scraper.py`** - Interface executável com configurações
3. **`config.py`** - Arquivo de configuração
4. **`setup.py`** - Script de instalação e configuração
5. **`demo_data.py`** - Gerador de dados de demonstração

### Fluxo de Dados

```
Configuração → Selenium WebDriver → Extração HTML → Processamento → Excel
```

## 🔧 Implementação Técnica

### Tecnologias Utilizadas

- **Selenium WebDriver**: Automação do navegador Chrome
- **BeautifulSoup4**: Parse de HTML (backup)
- **Pandas**: Manipulação de dados e exportação
- **OpenPyXL**: Geração de arquivos Excel
- **WebDriver Manager**: Gerenciamento automático do ChromeDriver

### Seletores CSS Implementados

```python
# Produtos
product_selectors = [
    ".product-card",
    "[data-ref='product-card']",
    ".product-item",
    ".grid-item",
    "article[data-module='product-card']"
]

# Imagens
img_selectors = [
    "img", 
    ".product-image img", 
    ".image img", 
    "[data-ref='product-image'] img"
]

# Títulos
title_selectors = [
    "[data-ref='product-title']",
    ".product-title",
    "h3", "h4",
    ".title",
    "a[title]"
]

# Avaliações
rating_selectors = [
    "[data-ref='star-rating']",
    ".star-rating",
    ".rating",
    ".stars"
]

# Preços
price_selectors = [
    "[data-ref='product-price']",
    ".price",
    ".product-price",
    ".current-price",
    ".selling-price"
]
```

### Expressões Regulares

```python
# Extração de avaliações (número entre parênteses)
review_pattern = r'\((\d+)\)'

# Extração de preços (formato R123,456.78)
price_pattern = r'R\s*(\d+(?:,\d+)*(?:\.\d+)?)'

# Extração de rating (número decimal)
rating_pattern = r'(\d+(?:\.\d+)?)'
```

## 📊 Estrutura de Dados

### Formato Interno (Python Dict)

```python
product_data = {
    'image_url': 'https://media.takealot.com/...',
    'description': 'Product Name...',
    'star_rating': 4.5,
    'review_count': 1247,
    'price': 12999.00
}
```

### Formato de Saída (Excel)

| Coluna | Tipo | Descrição |
|--------|------|-----------|
| Image URL | String | URL da imagem do produto |
| Product Description | String | Nome/descrição completa |
| Star Rating | Float | Classificação 0-5 estrelas |
| Number of Reviews | Integer | Número de avaliações |
| Sale Price | Float | Preço em Rands (R) |

## 🔄 Algoritmo de Ordenação

```python
def sort_products_by_reviews(products):
    """Ordena produtos por número de avaliações (decrescente)"""
    return sorted(products, key=lambda x: x.get('review_count', 0), reverse=True)
```

**Critério**: Número de avaliações em ordem decrescente
**Justificativa**: Produtos com mais avaliações são mais populares/confiáveis

## 🛡️ Tratamento de Erros

### Estratégias Implementadas

1. **Múltiplos Seletores**: Fallback para diferentes estruturas HTML
2. **Timeout Handling**: Aguarda carregamento de elementos
3. **Retry Logic**: Tenta novamente em caso de falha
4. **Graceful Degradation**: Continua mesmo se alguns campos falharem
5. **Logging Detalhado**: Registra todos os erros para debugging

### Exemplo de Implementação

```python
def extract_product_data(self, card):
    try:
        # Tenta múltiplos seletores
        for selector in title_selectors:
            try:
                element = card.find_element(By.CSS_SELECTOR, selector)
                if element.text:
                    return element.text
                    break
            except NoSuchElementException:
                continue
        
        # Valor padrão se nenhum funcionar
        return ""
        
    except Exception as e:
        logger.error(f"Error extracting data: {e}")
        return None
```

## ⚡ Otimizações de Performance

### Configurações do Chrome

```python
chrome_options = [
    "--headless",                    # Sem interface gráfica
    "--no-sandbox",                  # Bypass sandbox
    "--disable-dev-shm-usage",       # Otimização de memória
    "--disable-gpu",                 # Desabilita GPU
    "--window-size=1920,1080",       # Tamanho fixo
    "--user-agent=Mozilla/5.0..."    # User agent realista
]
```

### Delays Inteligentes

```python
REQUEST_DELAY = 2  # Segundos entre categorias
time.sleep(5)      # Aguarda carregamento da página
```

### Limitação de Produtos

```python
MAX_PRODUCTS = 100                # Total máximo
MAX_PRODUCTS_PER_CATEGORY = 20    # Por categoria
```

## 🔍 Validação de Dados

### Sanity Checks Implementados

```python
def validate_product_data(product):
    """Valida consistência dos dados"""
    
    # Produto deve ter descrição
    if not product.get('description'):
        return False
    
    # Rating deve estar entre 0-5
    rating = product.get('star_rating', 0)
    if rating < 0 or rating > 5:
        product['star_rating'] = 0
    
    # Preço deve ser positivo
    price = product.get('price', 0)
    if price < 0:
        product['price'] = 0
    
    # Review count deve ser não-negativo
    reviews = product.get('review_count', 0)
    if reviews < 0:
        product['review_count'] = 0
    
    return True
```

## 📈 Métricas e Monitoramento

### Logs Gerados

- **INFO**: Progresso normal, produtos extraídos
- **WARNING**: Elementos não encontrados, fallbacks
- **ERROR**: Falhas críticas, timeouts

### Estatísticas Coletadas

```python
statistics = {
    'total_products': len(products),
    'successful_categories': success_count,
    'average_reviews': mean(review_counts),
    'average_price': mean(prices),
    'average_rating': mean(ratings)
}
```

## 🔧 Configuração Avançada

### Personalização de Categorias

```python
# Adicionar novas categorias
CATEGORIES.append("https://www.takealot.com/new-category")

# Categorias específicas por interesse
ELECTRONICS = [
    "laptops", "smartphones", "tablets"
]

FASHION = [
    "mens-clothing", "womens-clothing", "shoes"
]
```

### Ajuste de Performance

```python
# Para scraping mais rápido (menos confiável)
HEADLESS_MODE = True
REQUEST_DELAY = 1
MAX_PRODUCTS_PER_CATEGORY = 10

# Para scraping mais confiável (mais lento)
HEADLESS_MODE = False
REQUEST_DELAY = 5
MAX_PRODUCTS_PER_CATEGORY = 50
```

## 🚨 Limitações Conhecidas

1. **Dependência do JavaScript**: Site requer renderização completa
2. **Mudanças na Estrutura**: Seletores podem quebrar com updates
3. **Rate Limiting**: Site pode bloquear muitas requisições
4. **Dados Dinâmicos**: Alguns preços podem ser carregados via AJAX
5. **Geolocalização**: Preços podem variar por região

## 🔮 Melhorias Futuras

### Funcionalidades Planejadas

1. **Cache de Dados**: Evitar re-scraping desnecessário
2. **Proxy Support**: Rotação de IPs para evitar bloqueios
3. **Async Processing**: Scraping paralelo de categorias
4. **Database Storage**: Armazenamento em SQLite/PostgreSQL
5. **API REST**: Interface web para configuração
6. **Scheduling**: Execução automática periódica
7. **Alertas**: Notificações de mudanças de preço

### Otimizações Técnicas

1. **Headless Chrome Otimizado**: Configurações mais eficientes
2. **Smart Selectors**: Auto-detecção de mudanças na estrutura
3. **Data Validation**: Verificação mais robusta de dados
4. **Error Recovery**: Recuperação automática de falhas
5. **Memory Management**: Otimização de uso de memória

## 📞 Suporte e Manutenção

### Debugging

```bash
# Executar com logs detalhados
python run_scraper.py --verbose

# Executar com browser visível
# Editar config.py: HEADLESS_MODE = False

# Verificar logs
cat logs/scraper_*.log
```

### Atualizações

1. **Seletores CSS**: Verificar se ainda funcionam
2. **Dependências**: Manter bibliotecas atualizadas
3. **Chrome Driver**: Atualizar conforme versão do Chrome
4. **Estrutura do Site**: Adaptar a mudanças no Takealot

---

**Desenvolvido seguindo as melhores práticas de web scraping ético e responsável.**
